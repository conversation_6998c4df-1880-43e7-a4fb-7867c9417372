import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";
import { ApiResponse } from "@/utils/types";

// GET /api/admin/cost-prices - Get products needing cost prices
export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    // Get products with their order information
    const products = await prisma.product.findMany({
      select: {
        id: true,
        name: true,
        brand: true,
        price: true,
        costPrice: true,
        costPriceUpdatedAt: true,
        costPriceUpdatedBy: true,
        images: true,
        orderItems: {
          select: {
            id: true,
            quantity: true,
            price: true,
            order: {
              select: {
                id: true,
                orderNumber: true,
                createdAt: true,
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      where: {
        isActive: true,
        orderItems: {
          some: {}, // Only products that have been ordered
        },
      },
      orderBy: [
        { costPrice: 'asc' }, // Products without cost price first (null values come first)
        { createdAt: 'desc' },
      ],
    });

    // Calculate statistics and format data
    const productsWithStats = products.map(product => {
      const orderCount = product.orderItems.reduce((sum, item) => sum + item.quantity, 0);
      const totalRevenue = product.orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const potentialProfit = product.costPrice 
        ? product.orderItems.reduce((sum, item) => sum + ((item.price - product.costPrice!) * item.quantity), 0)
        : null;

      return {
        id: product.id,
        name: product.name,
        brand: product.brand,
        price: product.price,
        costPrice: product.costPrice,
        costPriceUpdatedAt: product.costPriceUpdatedAt,
        costPriceUpdatedBy: product.costPriceUpdatedBy,
        images: product.images,
        orderCount,
        totalRevenue,
        potentialProfit,
      };
    });

    // Calculate overall statistics
    const stats = {
      totalProducts: products.length,
      productsWithCostPrice: products.filter(p => p.costPrice !== null).length,
      pendingCostPrices: products.filter(p => p.costPrice === null).length,
      totalPendingRevenue: productsWithStats
        .filter(p => p.costPrice === null)
        .reduce((sum, p) => sum + p.totalRevenue, 0),
    };

    // Filter to show products without cost price first, then products with cost price
    const prioritizedProducts = [
      ...productsWithStats.filter(p => p.costPrice === null),
      ...productsWithStats.filter(p => p.costPrice !== null),
    ];

    const response: ApiResponse<{ products: typeof prioritizedProducts; stats: typeof stats }> = {
      success: true,
      data: {
        products: prioritizedProducts,
        stats,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching cost prices:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch cost prices" },
      { status: 500 }
    );
  }
}

// POST /api/admin/cost-prices - Update product cost price
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { productId, costPrice } = body;

    if (!productId || !costPrice || costPrice <= 0) {
      return NextResponse.json(
        { success: false, error: "Valid product ID and cost price required" },
        { status: 400 }
      );
    }

    // Update the product cost price
    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: {
        costPrice: parseFloat(costPrice),
        costPriceUpdatedAt: new Date(),
        costPriceUpdatedBy: user.id,
      },
    });

    // Update all existing order items for this product with the cost price and calculate profit
    await prisma.orderItem.updateMany({
      where: { productId: productId },
      data: {
        costPrice: parseFloat(costPrice),
      },
    });

    // Calculate profit for each order item
    const orderItems = await prisma.orderItem.findMany({
      where: { productId: productId },
    });

    for (const item of orderItems) {
      const profit = (item.price - parseFloat(costPrice)) * item.quantity;
      await prisma.orderItem.update({
        where: { id: item.id },
        data: { profit },
      });
    }

    // Update order totals for orders containing this product
    const ordersToUpdate = await prisma.order.findMany({
      where: {
        orderItems: {
          some: { productId: productId },
        },
      },
      include: {
        orderItems: true,
      },
    });

    for (const order of ordersToUpdate) {
      const totalCostPrice = order.orderItems.reduce((sum, item) => {
        return sum + (item.costPrice ? item.costPrice * item.quantity : 0);
      }, 0);

      const totalProfit = order.orderItems.reduce((sum, item) => {
        return sum + (item.profit || 0);
      }, 0);

      // Only update if all items have cost prices
      const allItemsHaveCostPrice = order.orderItems.every(item => item.costPrice !== null);

      await prisma.order.update({
        where: { id: order.id },
        data: {
          totalCostPrice: allItemsHaveCostPrice ? totalCostPrice : null,
          totalProfit: allItemsHaveCostPrice ? totalProfit : null,
        },
      });
    }

    const response: ApiResponse<typeof updatedProduct> = {
      success: true,
      data: updatedProduct,
      message: "Cost price updated successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating cost price:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update cost price" },
      { status: 500 }
    );
  }
}
