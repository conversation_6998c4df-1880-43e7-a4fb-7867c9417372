"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  CreditCard,
  Clock,
  Package,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Eye,
  RefreshCw,
  XCircle,
  Ban,
} from "lucide-react";
import Link from "next/link";
import { formatPrice } from "@/lib/product-utils";
import {
  calculateDaysRemaining,
  calculatePaymentProgress,
  formatLayBuyPrice,
} from "@/lib/lay-buy-utils";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

interface LayBuyOrder {
  id: string;
  orderNumber: string;
  status: string;
  totalAmount: number;
  amountPaid: number;
  dueDate: string;
  gracePeriodEnd: string;
  createdAt: string;
  completedAt?: string;
  cancelledAt?: string;
  refundAmount?: number;
  orderItems: Array<{
    id: string;
    quantity: number;
    price: number;
    product: {
      id: string;
      name: string;
      images: string[];
    };
  }>;
}

interface LayBuyStats {
  activeOrders: number;
  totalOutstanding: number;
  completedOrders: number;
  cancelledOrders: number;
  totalPaid: number;
}

export default function LayBuyOrdersList() {
  const [orders, setOrders] = useState<LayBuyOrder[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<LayBuyOrder[]>([]);
  const [stats, setStats] = useState<LayBuyStats>({
    activeOrders: 0,
    totalOutstanding: 0,
    completedOrders: 0,
    cancelledOrders: 0,
    totalPaid: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState("ALL");

  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch("/api/lay-buy-orders");
      const result = await response.json();

      if (result.success) {
        setOrders(result.data);
        calculateStats(result.data);
      } else {
        setError(result.error || "Failed to fetch Lay-Buy orders");
      }
    } catch (err) {
      console.error("Error fetching Lay-Buy orders:", err);
      setError("Failed to fetch Lay-Buy orders");
    } finally {
      setIsLoading(false);
    }
  };

  const calculateStats = (orderList: LayBuyOrder[]) => {
    const stats = {
      activeOrders: orderList.filter(o => o.status === 'ACTIVE').length,
      totalOutstanding: orderList
        .filter(o => o.status === 'ACTIVE')
        .reduce((sum, o) => sum + (o.totalAmount - o.amountPaid), 0),
      completedOrders: orderList.filter(o => o.status === 'COMPLETED').length,
      cancelledOrders: orderList.filter(o => o.status === 'CANCELLED').length,
      totalPaid: orderList.reduce((sum, o) => sum + o.amountPaid, 0),
    };
    setStats(stats);
  };

  const filterOrders = (status: string) => {
    setStatusFilter(status);
    if (status === "ALL") {
      setFilteredOrders(orders);
    } else {
      setFilteredOrders(orders.filter(order => order.status === status));
    }
  };

  useEffect(() => {
    fetchOrders();

    // Refresh data when the page becomes visible (user returns to tab)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchOrders();
      }
    };

    // Refresh data when window gains focus (user returns to window)
    const handleFocus = () => {
      fetchOrders();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  useEffect(() => {
    filterOrders(statusFilter);
  }, [orders, statusFilter]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge className="bg-blue-100 text-blue-800">Active</Badge>;
      case 'COMPLETED':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'CANCELLED':
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
      case 'FORFEITED':
        return <Badge className="bg-gray-100 text-gray-800">Forfeited</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getCountdownDisplay = (order: LayBuyOrder) => {
    if (order.status !== 'ACTIVE') return null;

    const dueDate = new Date(order.dueDate);
    const gracePeriodEnd = new Date(order.gracePeriodEnd);
    const timeRemaining = calculateDaysRemaining(dueDate, gracePeriodEnd);

    if (timeRemaining.status === 'forfeited') {
      return (
        <div className="flex items-center gap-2 text-sm text-red-600">
          <Clock className="h-4 w-4" />
          <span>Payment period expired</span>
        </div>
      );
    } else if (timeRemaining.isInGracePeriod) {
      return (
        <div className="flex items-center gap-2 text-sm text-orange-600">
          <Clock className="h-4 w-4" />
          <span>Grace period: {timeRemaining.daysRemaining} days left</span>
        </div>
      );
    } else {
      const isUrgent = timeRemaining.daysRemaining <= 7;
      return (
        <div className={`flex items-center gap-2 text-sm ${
          isUrgent ? 'text-orange-600' : 'text-gray-600'
        }`}>
          <Clock className="h-4 w-4" />
          <span>{timeRemaining.daysRemaining} days remaining</span>
        </div>
      );
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchOrders} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Lay-Buy Orders</h1>
          <p className="text-gray-600 mt-1">Manage your Lay-Buy purchases and payments</p>
        </div>
        <Button onClick={fetchOrders} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.activeOrders}</div>
              <div className="text-sm text-blue-800">Active Orders</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{formatLayBuyPrice(stats.totalOutstanding)}</div>
              <div className="text-sm text-orange-800">Outstanding</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.completedOrders}</div>
              <div className="text-sm text-green-800">Completed</div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{stats.cancelledOrders}</div>
              <div className="text-sm text-red-800">Cancelled</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{formatLayBuyPrice(stats.totalPaid)}</div>
              <div className="text-sm text-purple-800">Total Paid</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filter */}
      <div className="flex gap-4 items-center">
        <span className="text-sm font-medium">Filter by status:</span>
        <Select value={statusFilter} onValueChange={filterOrders}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="All Orders" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">All Orders</SelectItem>
            <SelectItem value="ACTIVE">Active</SelectItem>
            <SelectItem value="COMPLETED">Completed</SelectItem>
            <SelectItem value="CANCELLED">Cancelled</SelectItem>
            <SelectItem value="FORFEITED">Forfeited</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Orders List */}
      {filteredOrders.length === 0 ? (
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">
            {statusFilter === "ALL" ? "No Lay-Buy orders found" : `No ${statusFilter.toLowerCase()} orders found`}
          </p>
        </div>
      ) : (
        <div className="grid gap-6">
          {filteredOrders.map((order) => {
            const paymentProgress = calculatePaymentProgress(order.amountPaid, order.totalAmount);
            const remainingBalance = order.totalAmount - order.amountPaid;

            return (
              <Card key={order.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold">Order {order.orderNumber}</h3>
                        {getStatusBadge(order.status)}
                      </div>
                      <div className="text-sm text-gray-600 space-y-1">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>Created: {new Date(order.createdAt).toLocaleDateString()}</span>
                        </div>
                        {order.status === 'ACTIVE' && (
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            <span>Due: {new Date(order.dueDate).toLocaleDateString()}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/lay-buy-orders/${order.id}`}>
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Link>
                    </Button>
                  </div>

                  <Separator className="my-4" />

                  {/* Order Items Preview */}
                  <div className="mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Package className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        {order.orderItems.length} item{order.orderItems.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {order.orderItems.slice(0, 2).map((item, index) => (
                        <span key={item.id}>
                          {item.quantity}x {item.product.name}
                          {index < Math.min(order.orderItems.length, 2) - 1 && ", "}
                        </span>
                      ))}
                      {order.orderItems.length > 2 && (
                        <span> and {order.orderItems.length - 2} more...</span>
                      )}
                    </div>
                  </div>

                  {/* Payment Information */}
                  {order.status === 'ACTIVE' && (
                    <>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Payment Progress</span>
                          <span>{paymentProgress.toFixed(1)}% Complete</span>
                        </div>
                        <Progress value={paymentProgress} className="h-2" />
                      </div>

                      <div className="grid grid-cols-3 gap-4 text-sm mt-4">
                        <div className="text-center">
                          <div className="font-semibold text-green-600">{formatLayBuyPrice(order.amountPaid ?? 0)}</div>
                          <div className="text-gray-600">Paid</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-orange-600">{formatLayBuyPrice(remainingBalance)}</div>
                          <div className="text-gray-600">Remaining</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-blue-600">{formatLayBuyPrice(order.totalAmount ?? 0)}</div>
                          <div className="text-gray-600">Total</div>
                        </div>
                      </div>

                      {getCountdownDisplay(order)}
                    </>
                  )}

                  {order.status === 'CANCELLED' && (
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-semibold text-green-600">{formatLayBuyPrice(order.amountPaid ?? 0)}</div>
                          <div className="text-gray-600">Amount Paid</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-red-600">
                            {order.refundAmount ? formatLayBuyPrice(order.refundAmount) : 'TBD'}
                          </div>
                          <div className="text-gray-600">Refund Amount</div>
                        </div>
                      </div>
                      
                      {order.cancelledAt && (
                        <div className="text-sm text-gray-600 text-center">
                          Cancelled on {new Date(order.cancelledAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  )}

                  {order.status === 'COMPLETED' && (
                    <div className="text-center text-sm text-green-600">
                      <div className="font-semibold">{formatLayBuyPrice(order.totalAmount)}</div>
                      <div>Fully Paid</div>
                      {order.completedAt && (
                        <div className="text-gray-600 mt-1">
                          Completed on {new Date(order.completedAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
