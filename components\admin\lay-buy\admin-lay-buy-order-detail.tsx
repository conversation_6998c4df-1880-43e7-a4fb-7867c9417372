"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ArrowLeft,
  User,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Package,
  CreditCard,
  Clock,
  CheckCircle,
  XCircle,
  Edit,
  Save,
  AlertTriangle,
  RefreshCw,
  Send,
  Ban,
  Trash2,
} from "lucide-react";
import Link from "next/link";
import { formatPrice } from "@/lib/product-utils";
import {
  calculateDaysRemaining,
  calculatePaymentProgress,
  formatLayBuyPrice,
} from "@/lib/lay-buy-utils";

interface AdminLayBuyOrderDetailProps {
  orderId: string;
}

interface LayBuyOrderDetailData {
  id: string;
  orderNumber: string;
  status: string;
  totalAmount: number;
  upfrontAmount: number;
  remainingAmount: number;
  amountPaid: number;
  dueDate: string;
  gracePeriodEnd: string;
  shippingAddress: string;
  phoneNumber: string;
  notes?: string;
  adminNotes?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  cancelledAt?: string;
  forfeitedAt?: string;
  refundAmount?: number;
  customer: {
    id: string;
    name: string;
    email: string;
  };
  orderItems: Array<{
    id: string;
    productName: string;
    productBrand: string;
    productImages: string[];
    quantity: number;
    price: number;
    size?: string;
    color?: string;
  }>;
  payments: Array<{
    id: string;
    amount: number;
    paymentType: string;
    paymentMethod?: string;
    paymentProof?: string;
    status: string;
    notes?: string;
    verifiedBy?: string;
    verifiedAt?: string;
    createdAt: string;
  }>;
  reminders: Array<{
    id: string;
    weekNumber: number;
    reminderType: string;
    emailSent: boolean;
    smsSent: boolean;
    sentAt: string;
  }>;
}

export default function AdminLayBuyOrderDetail({ orderId }: AdminLayBuyOrderDetailProps) {
  const [order, setOrder] = useState<LayBuyOrderDetailData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [newStatus, setNewStatus] = useState<string>("");
  const [adminNotes, setAdminNotes] = useState("");
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [isSendingReminder, setIsSendingReminder] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showForfeitDialog, setShowForfeitDialog] = useState(false);
  const [cancelReason, setCancelReason] = useState("");
  const [forfeitReason, setForfeitReason] = useState("");

  useEffect(() => {
    fetchOrderDetail();
  }, [orderId]);

  const fetchOrderDetail = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/lay-buy-orders/${orderId}`);
      const result = await response.json();

      if (result.success) {
        const orderData = result.data;
        const formattedOrder: LayBuyOrderDetailData = {
          id: orderData.id,
          orderNumber: orderData.orderNumber,
          status: orderData.status,
          totalAmount: orderData.totalAmount,
          upfrontAmount: orderData.upfrontAmount,
          remainingAmount: orderData.remainingAmount,
          amountPaid: orderData.amountPaid,
          dueDate: orderData.dueDate,
          gracePeriodEnd: orderData.gracePeriodEnd,
          shippingAddress: orderData.shippingAddress,
          phoneNumber: orderData.phoneNumber,
          notes: orderData.notes,
          adminNotes: orderData.adminNotes,
          createdAt: orderData.createdAt,
          updatedAt: orderData.updatedAt,
          completedAt: orderData.completedAt,
          cancelledAt: orderData.cancelledAt,
          forfeitedAt: orderData.forfeitedAt,
          refundAmount: orderData.refundAmount,
          customer: {
            id: orderData.user.id,
            name: orderData.user.name,
            email: orderData.user.email,
          },
          orderItems: orderData.orderItems.map((item: any) => ({
            id: item.id,
            productName: item.product.name,
            productBrand: item.product.brand,
            productImages: item.product.images,
            quantity: item.quantity,
            price: item.price,
            size: item.size,
            color: item.color,
          })),
          payments: orderData.payments || [],
          reminders: orderData.reminders || [],
        };
        
        setOrder(formattedOrder);
        setNewStatus(formattedOrder.status);
        setAdminNotes(formattedOrder.adminNotes || "");
      }
    } catch (error) {
      console.error("Error fetching order details:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusUpdate = async () => {
    if (!order || newStatus === order.status) return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/admin/lay-buy-orders/${order.id}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
          adminNotes: adminNotes,
        }),
      });

      const result = await response.json();
      if (result.success) {
        await fetchOrderDetail(); // Refresh the data
      }
    } catch (error) {
      console.error("Error updating order status:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleNotesUpdate = async () => {
    if (!order) return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/admin/lay-buy-orders/${order.id}/notes`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          adminNotes: adminNotes,
        }),
      });

      const result = await response.json();
      if (result.success) {
        setOrder({ ...order, adminNotes });
        setIsEditingNotes(false);
      }
    } catch (error) {
      console.error("Error updating admin notes:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleSendReminder = async () => {
    if (!order) return;

    setIsSendingReminder(true);
    try {
      const response = await fetch(`/api/admin/lay-buy-orders/${order.id}/send-reminder`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({}), // Let the system determine the appropriate reminder type
      });

      const result = await response.json();
      if (result.success) {
        await fetchOrderDetail(); // Refresh the data to show the new reminder
        // You might want to show a success toast here
      } else {
        console.error("Failed to send reminder:", result.error);
        // You might want to show an error toast here
      }
    } catch (error) {
      console.error("Error sending reminder:", error);
    } finally {
      setIsSendingReminder(false);
    }
  };

  const handleCancelOrder = async () => {
    if (!order) return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/admin/lay-buy-orders/${order.id}/cancel`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reason: cancelReason.trim() || "Cancelled by admin",
          adminNotes: `Order cancelled: ${cancelReason.trim() || "No reason provided"}`,
        }),
      });

      const result = await response.json();
      if (result.success) {
        await fetchOrderDetail(); // Refresh the data
        setShowCancelDialog(false);
        setCancelReason("");
      } else {
        console.error("Failed to cancel order:", result.error);
      }
    } catch (error) {
      console.error("Error cancelling order:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleForfeitOrder = async () => {
    if (!order) return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/admin/lay-buy-orders/${order.id}/forfeit`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reason: forfeitReason.trim() || "Forfeited due to non-payment",
        }),
      });

      const result = await response.json();
      if (result.success) {
        await fetchOrderDetail(); // Refresh the data
        setShowForfeitDialog(false);
        setForfeitReason("");
      } else {
        console.error("Failed to forfeit order:", result.error);
      }
    } catch (error) {
      console.error("Error forfeiting order:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="text-center py-12">
        <XCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Order Not Found</h2>
        <p className="text-gray-600 mb-6">The requested Lay-Buy order could not be found.</p>
        <Link href="/admin/lay-buy-orders">
          <Button>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Lay-Buy Orders
          </Button>
        </Link>
      </div>
    );
  }

  const dueDate = new Date(order.dueDate);
  const gracePeriodEnd = new Date(order.gracePeriodEnd);
  const timeRemaining = calculateDaysRemaining(dueDate, gracePeriodEnd);
  const paymentProgress = calculatePaymentProgress(order.amountPaid, order.totalAmount);
  const remainingBalance = order.totalAmount - order.amountPaid;

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      ACTIVE: { color: "bg-blue-100 text-blue-800", label: "Active" },
      COMPLETED: { color: "bg-green-100 text-green-800", label: "Completed" },
      CANCELLED: { color: "bg-gray-100 text-gray-800", label: "Cancelled" },
      FORFEITED: { color: "bg-red-100 text-red-800", label: "Forfeited" },
      REFUNDED: { color: "bg-purple-100 text-purple-800", label: "Refunded" },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.ACTIVE;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getUrgencyAlert = () => {
    if (order.status !== 'ACTIVE') return null;
    
    if (timeRemaining.status === 'forfeited') {
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-semibold">Payment Period Expired</span>
          </div>
          <p className="text-sm text-red-700 mt-1">
            This order should be marked as forfeited as the grace period has ended.
          </p>
        </div>
      );
    } else if (timeRemaining.isInGracePeriod) {
      return (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-orange-800">
            <Clock className="h-5 w-5" />
            <span className="font-semibold">Grace Period - {timeRemaining.daysRemaining} days left</span>
          </div>
          <p className="text-sm text-orange-700 mt-1">
            Customer is in the grace period. Send urgent reminders.
          </p>
        </div>
      );
    } else if (timeRemaining.daysRemaining <= 7) {
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-yellow-800">
            <Clock className="h-5 w-5" />
            <span className="font-semibold">Due Soon - {timeRemaining.daysRemaining} days remaining</span>
          </div>
          <p className="text-sm text-yellow-700 mt-1">
            Payment is due soon. Consider sending reminder emails.
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/lay-buy-orders">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Lay-Buy Order Details</h1>
            <p className="text-gray-600">Order #{order.orderNumber}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(order.status)}
          {order.status === 'ACTIVE' && (
            <Button
              onClick={handleSendReminder}
              variant="outline"
              size="sm"
              disabled={isSendingReminder}
            >
              {isSendingReminder ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Reminder
                </>
              )}
            </Button>
          )}
          <Button onClick={fetchOrderDetail} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Urgency Alert */}
      {getUrgencyAlert()}

      {/* Status Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Order Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Order Status
              </label>
              <Select value={newStatus} onValueChange={setNewStatus}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  <SelectItem value="FORFEITED">Forfeited</SelectItem>
                  <SelectItem value="REFUNDED">Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button 
                onClick={handleStatusUpdate}
                disabled={isUpdating || newStatus === order.status}
                className="w-full"
              >
                {isUpdating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Update Status
                  </>
                )}
              </Button>
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Admin Notes
              </label>
              {!isEditingNotes && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditingNotes(true)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
            </div>
            {isEditingNotes ? (
              <div className="space-y-2">
                <Textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add internal notes about this order..."
                  rows={3}
                />
                <div className="flex gap-2">
                  <Button onClick={handleNotesUpdate} disabled={isUpdating}>
                    <Save className="h-4 w-4 mr-2" />
                    Save Notes
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setIsEditingNotes(false);
                      setAdminNotes(order.adminNotes || "");
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">
                  {order.adminNotes || "No admin notes added yet."}
                </p>
              </div>
            )}
          </div>

          {/* Order Actions - Only show for active orders */}
          {order.status === 'ACTIVE' && (
            <div className="pt-4 border-t">
              <h4 className="font-medium text-gray-900 mb-3">Order Actions</h4>
              <div className="flex gap-2">
                <Button
                  onClick={() => setShowCancelDialog(true)}
                  variant="outline"
                  className="flex-1 border-orange-300 text-orange-700 hover:bg-orange-50"
                >
                  <Ban className="h-4 w-4 mr-2" />
                  Cancel Order
                </Button>
                <Button
                  onClick={() => setShowForfeitDialog(true)}
                  variant="destructive"
                  className="flex-1"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Forfeit Order
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Cancel: Customer gets 50% refund • Forfeit: No refund, order expires
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Customer & Order Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-3">
              <User className="h-4 w-4 text-gray-500" />
              <div>
                <p className="font-medium">{order.customer.name}</p>
                <p className="text-sm text-gray-600">{order.customer.email}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="h-4 w-4 text-gray-500" />
              <p className="text-sm">{order.phoneNumber}</p>
            </div>
            <div className="flex items-start gap-3">
              <MapPin className="h-4 w-4 text-gray-500 mt-1" />
              <div>
                <p className="font-medium">Shipping Address</p>
                <p className="text-sm text-gray-600">{order.shippingAddress}</p>
              </div>
            </div>
            {order.notes && (
              <div className="flex items-start gap-3">
                <Mail className="h-4 w-4 text-gray-500 mt-1" />
                <div>
                  <p className="font-medium">Customer Notes</p>
                  <p className="text-sm text-gray-600">{order.notes}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Payment Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{paymentProgress.toFixed(1)}% Complete</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${paymentProgress}%` }}
                ></div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-2 text-sm">
              <div className="text-center p-2 bg-green-50 rounded">
                <div className="font-semibold text-green-800">Paid</div>
                <div className="text-green-600">{formatLayBuyPrice(order.amountPaid ?? 0)}</div>
              </div>
              <div className="text-center p-2 bg-orange-50 rounded">
                <div className="font-semibold text-orange-800">Remaining</div>
                <div className="text-orange-600">{formatLayBuyPrice(remainingBalance)}</div>
              </div>
              <div className="text-center p-2 bg-blue-50 rounded">
                <div className="font-semibold text-blue-800">Total</div>
                <div className="text-blue-600">{formatLayBuyPrice(order.totalAmount ?? 0)}</div>
              </div>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Due Date:</span>
                <span className="font-medium">{dueDate.toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Grace Period Ends:</span>
                <span className="font-medium">{gracePeriodEnd.toLocaleDateString()}</span>
              </div>
              {order.status === 'ACTIVE' && (
                <div className="flex justify-between">
                  <span>Days Remaining:</span>
                  <span className={`font-medium ${
                    timeRemaining.isInGracePeriod ? 'text-orange-600' :
                    timeRemaining.daysRemaining <= 7 ? 'text-yellow-600' : 'text-green-600'
                  }`}>
                    {timeRemaining.daysRemaining} days
                    {timeRemaining.isInGracePeriod && ' (Grace Period)'}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Items */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Order Items ({order.orderItems.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {order.orderItems.map((item) => (
              <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                  {item.productImages.length > 0 ? (
                    <img
                      src={item.productImages[0]}
                      alt={item.productName}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <Package className="h-6 w-6 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold">{item.productName}</h3>
                  <p className="text-sm text-gray-600">{item.productBrand}</p>
                  {(item.size || item.color) && (
                    <p className="text-xs text-gray-500">
                      {item.size && `Size: ${item.size}`}
                      {item.size && item.color && " • "}
                      {item.color && `Color: ${item.color}`}
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <div className="font-semibold">{formatPrice(item.price)}</div>
                  <div className="text-sm text-gray-600">Qty: {item.quantity}</div>
                  <div className="text-sm text-gray-500">
                    Total: {formatPrice(item.price * item.quantity)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Payment History */}
      {order.payments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment History ({order.payments.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {order.payments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className={`w-3 h-3 rounded-full ${
                      payment.status === 'VERIFIED' ? 'bg-green-500' :
                      payment.status === 'REJECTED' ? 'bg-red-500' : 'bg-yellow-500'
                    }`} />
                    <div>
                      <p className="font-medium">{formatLayBuyPrice(payment.amount)}</p>
                      <p className="text-sm text-gray-600">
                        {payment.paymentType} • {new Date(payment.createdAt).toLocaleDateString()}
                        {payment.paymentMethod && ` • ${payment.paymentMethod}`}
                      </p>
                      {payment.notes && (
                        <p className="text-xs text-gray-500 mt-1">{payment.notes}</p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge className={
                      payment.status === 'VERIFIED' ? 'bg-green-100 text-green-800' :
                      payment.status === 'REJECTED' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }>
                      {payment.status}
                    </Badge>
                    {payment.verifiedAt && (
                      <p className="text-xs text-gray-500 mt-1">
                        Verified: {new Date(payment.verifiedAt).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reminder History */}
      {order.reminders.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Reminder History ({order.reminders.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {order.reminders.map((reminder) => (
                <div key={reminder.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Week {reminder.weekNumber} - {reminder.reminderType}</p>
                    <p className="text-sm text-gray-600">
                      Sent: {new Date(reminder.sentAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    {reminder.emailSent && (
                      <Badge className="bg-blue-100 text-blue-800">Email Sent</Badge>
                    )}
                    {reminder.smsSent && (
                      <Badge className="bg-green-100 text-green-800">SMS Sent</Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Cancel Order Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Ban className="h-5 w-5 text-orange-600" />
              Cancel Lay-Buy Order
            </DialogTitle>
            <DialogDescription>
              Cancel order {order.orderNumber}? The customer will receive a 50% refund of their payments.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cancellation Reason
              </label>
              <Textarea
                value={cancelReason}
                onChange={(e) => setCancelReason(e.target.value)}
                placeholder="Enter reason for cancellation..."
                rows={3}
              />
            </div>

            <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
              <p className="text-sm text-orange-800">
                <strong>Refund Amount:</strong> 50% of payments made = {formatLayBuyPrice(order.amountPaid * 0.5)}
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCancelDialog(false)}>
              Keep Order
            </Button>
            <Button
              onClick={handleCancelOrder}
              disabled={isUpdating}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isUpdating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Cancelling...
                </>
              ) : (
                <>
                  <Ban className="h-4 w-4 mr-2" />
                  Cancel Order
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Forfeit Order Dialog */}
      <Dialog open={showForfeitDialog} onOpenChange={setShowForfeitDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              Forfeit Lay-Buy Order
            </DialogTitle>
            <DialogDescription>
              Forfeit order {order.orderNumber}? This action cannot be undone and the customer will receive no refund.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Forfeiture Reason
              </label>
              <Textarea
                value={forfeitReason}
                onChange={(e) => setForfeitReason(e.target.value)}
                placeholder="Enter reason for forfeiture (e.g., payment deadline exceeded)..."
                rows={3}
              />
            </div>

            <div className="bg-red-50 p-3 rounded-lg border border-red-200">
              <p className="text-sm text-red-800">
                <strong>Warning:</strong> Customer will forfeit all payments made ({formatLayBuyPrice(order.amountPaid)}) and receive no refund.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowForfeitDialog(false)}>
              Keep Order
            </Button>
            <Button
              onClick={handleForfeitOrder}
              disabled={isUpdating}
              variant="destructive"
            >
              {isUpdating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Forfeiting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Forfeit Order
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
