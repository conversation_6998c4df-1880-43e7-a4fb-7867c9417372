"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import AdminLayout from "@/components/admin/admin-layout";
import ProductForm, {
  Category,
} from "@/components/admin/products/product-form";
import { getCategories } from "@/actions/categoryActions";
import { User } from "@/utils/types";
import { getCurrentUser } from "@/lib/auth-utils";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

export default function AdminAddProductPage() {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [error, setError] = useState("");
  const [user, setUser] = useState<User | null | undefined>(null);

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const result = await getCategories();
        if (result.success && result.data) {
          setCategories(
            result.data.map((cat: any) => ({
              ...cat,
              description:
                cat.description === null ? undefined : cat.description,
            }))
          );
        }
        const userFromDb = await getCurrentUser();
        setUser(userFromDb);
      } catch (error) {
        console.error("Error loading categories:", error);
      } finally {
        setLoadingCategories(false);
      }
    };
    loadCategories();
  }, []);

  const handleCreate = async (data: any) => {
    setLoading(true);
    setError("");
    try {
      const res = await fetch("/api/admin/products", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      const result = await res.json();
      if (result.success) {
        router.push("/admin/products");
      } else {
        setError(result.error || "Failed to add product");
      }
    } catch (err) {
      setError("Failed to add product");
    } finally {
      setLoading(false);
    }
  };

  if (loadingCategories) {
    return (
      <div className="flex justify-center items-center w-full h-screen">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (!user) {
    router.push("/sign-in");
    return (
      <div className="flex justify-center items-center w-full h-screen">
        <SpinnerCircle4 />
      </div>
    );
  }

  return (
    <AdminLayout user={user}>
      <div className="max-w-4xl mx-auto py-8 px-4">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Add New Product</h1>
          <p className="text-gray-600 mt-2">
            Create a new product for your store
          </p>
        </div>
        <ProductForm
          categories={categories}
          onSubmit={handleCreate}
          loading={loading}
          error={error}
          mode="create"
        />
      </div>
    </AdminLayout>
  );
}
