import { Resend } from "resend";

if (!process.env.RESEND_API_KEY) {
  throw new Error("RESEND_API_KEY is not set in environment variables");
}

export const resend = new Resend(process.env.RESEND_API_KEY);

// Email configuration
export const EMAIL_CONFIG = {
  from: process.env.EMAIL_FROM || "RIVV Premium Sneakers <<EMAIL>>",
  replyTo: "<EMAIL>",
  baseUrl: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
};

// Email types
export interface EmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
}

// Send email function
export async function sendEmail(options: EmailOptions) {
  try {
    const result = await resend.emails.send({
      from: EMAIL_CONFIG.from,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
      replyTo: EMAIL_CONFIG.replyTo,
    });

    return { success: true, data: result };
  } catch (error) {
    console.error("Error sending email:", error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to send email" };
  }
}

// Email templates
export const emailTemplates = {
  // Order confirmation email (PENDING status)
  orderConfirmation: (data: {
    customerName: string;
    orderNumber: string;
    orderTotal: string;
    orderItems: Array<{
      name: string;
      quantity: number;
      price: string;
      size?: string;
    }>;
    shippingAddress: string;
    phoneNumber?: string;
    orderUrl: string;
    upfrontAmount?: string;
    remainingAmount?: string;
    dueDate?: string;
    isLayBuy?: boolean;
  }) => ({
    subject: `Order Confirmation - ${data.orderNumber} | RIVV`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Order Confirmation</title>
          <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #1f2937 0%, #374151 100%); color: white; padding: 30px 20px; text-align: center; }
            .brand { font-size: 28px; font-weight: bold; margin-bottom: 8px; letter-spacing: 2px; }
            .tagline { font-size: 14px; opacity: 0.9; font-style: italic; }
            .content { padding: 30px 20px; }
            .status-badge { display: inline-block; background-color: #fbbf24; color: #92400e; padding: 8px 16px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; margin-bottom: 20px; }
            .order-details { background-color: #f9fafb; padding: 25px; border-radius: 12px; margin: 25px 0; border-left: 4px solid #1f2937; }
            .order-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; }
            .order-number { font-size: 18px; font-weight: bold; color: #1f2937; }
            .item { border-bottom: 1px solid #e5e7eb; padding: 15px 0; display: flex; justify-content: space-between; align-items: center; }
            .item:last-child { border-bottom: none; }
            .item-info { flex: 1; }
            .item-name { font-weight: 600; color: #1f2937; margin-bottom: 4px; }
            .item-details { font-size: 14px; color: #6b7280; }
            .item-price { font-weight: bold; color: #1f2937; }
            .total-section { background-color: #1f2937; color: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .total { font-weight: bold; font-size: 24px; text-align: center; }
            .button { display: inline-block; background: linear-gradient(135deg, #1f2937 0%, #374151 100%); color: white; padding: 14px 28px; text-decoration: none; border-radius: 8px; margin: 25px 0; font-weight: 600; text-align: center; }
            .next-steps { background-color: #eff6ff; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6; margin: 25px 0; }
            .contact-info { background-color: #f0fdf4; padding: 20px; border-radius: 8px; border-left: 4px solid #10b981; margin: 25px 0; }
            .footer { background-color: #f9fafb; padding: 25px 20px; text-align: center; color: #6b7280; font-size: 14px; border-top: 1px solid #e5e7eb; }
            .unsubscribe { font-size: 12px; color: #9ca3af; margin-top: 15px; }
            .unsubscribe a { color: #9ca3af; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="brand">RIVV</div>
              <div class="tagline">Purposefully Curated. Unapologetically Premium.</div>
            </div>

            <div class="content">
              <div class="status-badge">Order Pending</div>

              <h2>Thank you for your order, ${data.customerName}!</h2>
              <p>We've received your order and it's currently pending payment verification. Here are the details:</p>

              <div class="order-details">
                <div class="order-header">
                  <div class="order-number">Order #${data.orderNumber}</div>
                </div>

                <h4 style="margin-bottom: 15px; color: #1f2937;">Items Ordered:</h4>
                ${data.orderItems.map(item => `
                  <div class="item">
                    <div class="item-info">
                      <div class="item-name">${item.name}</div>
                      <div class="item-details">
                        Quantity: ${item.quantity}${item.size ? ` • Size: ${item.size}` : ''}
                      </div>
                    </div>
                    <div class="item-price">${item.price}</div>
                  </div>
                `).join('')}

                <div class="total-section">
                  <div class="total">Total: ${data.orderTotal}</div>
                </div>

                <h4 style="margin: 20px 0 10px 0; color: #1f2937;">Shipping Details:</h4>
                <p style="margin: 0; line-height: 1.5;"><strong>Address:</strong> ${data.shippingAddress}</p>
                <p style="margin: 5px 0 0 0; line-height: 1.5;"><strong>Phone:</strong> ${data.phoneNumber}</p>
              </div>

              <div style="text-align: center;">
                <a href="${data.orderUrl}" class="button">View Order Details</a>
              </div>

              <div class="next-steps">
                <h4 style="margin-top: 0; color: #1e40af;">What happens next?</h4>
                <ul style="margin: 0; padding-left: 20px;">
                  <li>We'll verify your payment within 24 hours</li>
                  <li>Once verified, your order status will change to "Confirmed"</li>
                  <li>We'll then process and prepare your items for shipment</li>
                  <li>You'll receive email updates at each step of the process</li>
                </ul>
              </div>

              <div class="contact-info">
                <h4 style="margin-top: 0; color: #059669;">Need Help?</h4>
                <p style="margin: 0;">For any questions or concerns, contact our support team:</p>
                <p style="margin: 5px 0 0 0;"><strong>WhatsApp:</strong> +266 62844473</p>
                <p style="margin: 5px 0 0 0;"><strong>Email:</strong> <EMAIL></p>
              </div>
            </div>

            <div class="footer">
              <p><strong>RIVV Premium Sneakers</strong><br>
              Purposefully Curated. Unapologetically Premium.</p>
              <p>© 2025 RIVV. All rights reserved.</p>
              <div class="unsubscribe">
                <p>You received this email because you placed an order with us.<br>
                <a href="#">Unsubscribe from marketing emails</a></p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      RIVV - Order Confirmation
      Purposefully Curated. Unapologetically Premium.

      ORDER PENDING

      Thank you for your order, ${data.customerName}!

      We've received your order and it's currently pending payment verification.

      Order #${data.orderNumber}

      Items Ordered:
      ${data.orderItems.map(item => `${item.name} - Qty: ${item.quantity}${item.size ? ` (Size: ${item.size})` : ''} - ${item.price}`).join('\n')}

      Total: ${data.orderTotal}

      Shipping Details:
      Address: ${data.shippingAddress}
      Phone: ${data.phoneNumber}

      View order details: ${data.orderUrl}

      What happens next?
      - We'll verify your payment within 24 hours
      - Once verified, your order status will change to "Confirmed"
      - We'll then process and prepare your items for shipment
      - You'll receive email updates at each step of the process

      Need Help?
      WhatsApp: +266 62844473
      Email: <EMAIL>

      RIVV Premium Sneakers
      © 2025 RIVV. All rights reserved.
    `,
  }),

  // Admin order notification email
  adminOrderNotification: (data: {
    orderNumber: string;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
    orderTotal: string;
    orderItems: Array<{
      name: string;
      quantity: number;
      price: string;
      size?: string;
    }>;
    shippingAddress: string;
    paymentMethod?: string;
    notes?: string;
    orderUrl: string;
    createdAt: string;
  }) => ({
    subject: `🔔 New Order Received - ${data.orderNumber} | RIVV Admin`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>New Order Notification</title>
          <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 30px 20px; text-align: center; }
            .brand { font-size: 28px; font-weight: bold; margin-bottom: 8px; letter-spacing: 2px; }
            .admin-badge { font-size: 14px; background-color: rgba(255,255,255,0.2); padding: 4px 12px; border-radius: 12px; }
            .content { padding: 30px 20px; }
            .alert-badge { display: inline-block; background-color: #dc2626; color: white; padding: 8px 16px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; margin-bottom: 20px; }
            .order-details { background-color: #f9fafb; padding: 25px; border-radius: 12px; margin: 25px 0; border-left: 4px solid #dc2626; }
            .customer-info { background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
            .item { border-bottom: 1px solid #e5e7eb; padding: 15px 0; display: flex; justify-content: space-between; align-items: center; }
            .item:last-child { border-bottom: none; }
            .item-info { flex: 1; }
            .item-name { font-weight: 600; color: #1f2937; margin-bottom: 4px; }
            .item-details { font-size: 14px; color: #6b7280; }
            .item-price { font-weight: bold; color: #1f2937; }
            .total-section { background-color: #dc2626; color: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .total { font-weight: bold; font-size: 24px; text-align: center; }
            .button { display: inline-block; background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 14px 28px; text-decoration: none; border-radius: 8px; margin: 25px 0; font-weight: 600; text-align: center; }
            .footer { background-color: #f9fafb; padding: 25px 20px; text-align: center; color: #6b7280; font-size: 14px; border-top: 1px solid #e5e7eb; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="brand">RIVV</div>
              <div class="admin-badge">ADMIN NOTIFICATION</div>
            </div>

            <div class="content">
              <div class="alert-badge">🔔 New Order</div>

              <h2>New Order Received!</h2>
              <p>A new order has been placed and requires your attention.</p>

              <div class="customer-info">
                <h4 style="margin-top: 0; color: #92400e;">Customer Information</h4>
                <p style="margin: 5px 0;"><strong>Name:</strong> ${data.customerName}</p>
                <p style="margin: 5px 0;"><strong>Email:</strong> ${data.customerEmail}</p>
                <p style="margin: 5px 0;"><strong>Phone:</strong> ${data.customerPhone}</p>
                <p style="margin: 5px 0;"><strong>Order Date:</strong> ${data.createdAt}</p>
              </div>

              <div class="order-details">
                <h3 style="margin-top: 0; color: #1f2937;">Order #${data.orderNumber}</h3>

                <h4 style="margin-bottom: 15px; color: #1f2937;">Items Ordered:</h4>
                ${data.orderItems.map(item => `
                  <div class="item">
                    <div class="item-info">
                      <div class="item-name">${item.name}</div>
                      <div class="item-details">
                        Quantity: ${item.quantity}${item.size ? ` • Size: ${item.size}` : ''}
                      </div>
                    </div>
                    <div class="item-price">${item.price}</div>
                  </div>
                `).join('')}

                <div class="total-section">
                  <div class="total">Total: ${data.orderTotal}</div>
                </div>

                <h4 style="margin: 20px 0 10px 0; color: #1f2937;">Shipping Details:</h4>
                <p style="margin: 0; line-height: 1.5;"><strong>Address:</strong> ${data.shippingAddress}</p>

                ${data.paymentMethod ? `<p style="margin: 5px 0 0 0; line-height: 1.5;"><strong>Payment Method:</strong> ${data.paymentMethod}</p>` : ''}
                ${data.notes ? `<p style="margin: 10px 0 0 0; line-height: 1.5;"><strong>Notes:</strong> ${data.notes}</p>` : ''}
              </div>

              <div style="text-align: center;">
                <a href="${data.orderUrl}" class="button">View Order in Admin Panel</a>
              </div>
            </div>

            <div class="footer">
              <p><strong>RIVV Admin Panel</strong><br>
              Order Management System</p>
              <p>© 2025 RIVV. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      RIVV - New Order Notification
      ADMIN NOTIFICATION

      🔔 NEW ORDER RECEIVED

      A new order has been placed and requires your attention.

      Customer Information:
      Name: ${data.customerName}
      Email: ${data.customerEmail}
      Phone: ${data.customerPhone}
      Order Date: ${data.createdAt}

      Order #${data.orderNumber}

      Items Ordered:
      ${data.orderItems.map(item => `${item.name} - Qty: ${item.quantity}${item.size ? ` (Size: ${item.size})` : ''} - ${item.price}`).join('\n')}

      Total: ${data.orderTotal}

      Shipping Details:
      Address: ${data.shippingAddress}
      ${data.paymentMethod ? `Payment Method: ${data.paymentMethod}` : ''}
      ${data.notes ? `Notes: ${data.notes}` : ''}

      View order in admin panel: ${data.orderUrl}

      RIVV Admin Panel
      © 2025 RIVV. All rights reserved.
    `,
  }),

  // Contact form submission confirmation
  contactConfirmation: (data: {
    customerName: string;
    subject: string;
  }) => ({
    subject: "We've received your message",
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Message Received</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; margin-bottom: 20px; }
            .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Message Received</h1>
            </div>
            
            <p>Hi ${data.customerName},</p>
            
            <p>Thank you for contacting us regarding "${data.subject}". We've received your message and will get back to you within 24 hours.</p>
            
            <p>Our support team is reviewing your inquiry and will provide you with a detailed response soon.</p>
            
            <p><strong>Response Times:</strong></p>
            <ul>
              <li>General inquiries: Within 24 hours</li>
              <li>Order support: Within 4-6 hours during business hours</li>
              <li>Technical issues: Within 12 hours</li>
            </ul>
            
            <p>For urgent matters, you can also call us directly at +266 62844473.</p>
            
            <div class="footer">
              <p>Best regards,<br>Rivv Support Team</p>
              <p>© 2025 RIVV Premium Sneakers. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      Message Received
      
      Hi ${data.customerName},
      
      Thank you for contacting us regarding "${data.subject}". We've received your message and will get back to you within 24 hours.
      
      Our support team is reviewing your inquiry and will provide you with a detailed response soon.
      
      Response Times:
      - General inquiries: Within 24 hours
      - Order support: Within 4-6 hours during business hours
      - Technical issues: Within 12 hours
      
      For urgent matters, you can also call us directly at +266 62844473.
      
      Best regards,
      Rivv Support Team
    `,
  }),

  // Admin notification for new contact message
  newContactMessage: (data: {
    customerName: string;
    customerEmail: string;
    subject: string;
    message: string;
    messageUrl: string;
  }) => ({
    subject: `New Contact Message: ${data.subject}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>New Contact Message</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; margin-bottom: 20px; }
            .message-details { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .button { display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>New Contact Message</h1>
            </div>
            
            <p>A new contact message has been received:</p>
            
            <div class="message-details">
              <p><strong>From:</strong> ${data.customerName} (${data.customerEmail})</p>
              <p><strong>Subject:</strong> ${data.subject}</p>
              <p><strong>Message:</strong></p>
              <p>${data.message}</p>
            </div>
            
            <a href="${data.messageUrl}" class="button">View in Admin Panel</a>
          </div>
        </body>
      </html>
    `,
    text: `
      New Contact Message
      
      A new contact message has been received:
      
      From: ${data.customerName} (${data.customerEmail})
      Subject: ${data.subject}
      
      Message:
      ${data.message}
      
      View in admin panel: ${data.messageUrl}
    `,
  }),

  // Password reset email
  passwordReset: (data: {
    customerName: string;
    resetUrl: string;
  }) => ({
    subject: "Reset Your Password",
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; margin-bottom: 20px; }
            .button { display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .warning { background-color: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Reset Your Password</h1>
            </div>
            
            <p>Hi ${data.customerName},</p>
            
            <p>We received a request to reset your password for your Rivv account. Click the button below to create a new password:</p>
            
            <a href="${data.resetUrl}" class="button">Reset Password</a>
            
            <div class="warning">
              <p><strong>Security Notice:</strong></p>
              <ul>
                <li>This link will expire in 1 hour for security reasons</li>
                <li>If you didn't request this reset, please ignore this email</li>
                <li>Never share this link with anyone</li>
              </ul>
            </div>
            
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p>${data.resetUrl}</p>
            
            <div class="footer">
              <p>If you have any questions, please contact <NAME_EMAIL></p>
              <p>© 2025 RIVV Premium Sneakers. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      Reset Your Password
      
      Hi ${data.customerName},
      
      We received a request to reset your password for your Rivv account. Click the link below to create a new password:
      
      ${data.resetUrl}
      
      Security Notice:
      - This link will expire in 1 hour for security reasons
      - If you didn't request this reset, please ignore this email
      - Never share this link with anyone
      
      If you have any questions, please contact <NAME_EMAIL>
    `,
  }),

  // Enhanced order status update templates
  orderStatusUpdate: (data: {
    customerName: string;
    orderNumber: string;
    status: string;
    statusMessage: string;
    orderTotal: string;
    orderItems: Array<{
      name: string;
      quantity: number;
      price: string;
      size?: string;
    }>;
    orderUrl: string;
    trackingInfo?: string;
    estimatedDelivery?: string;
    cancellationReason?: string;
  }) => {
    const statusConfig = {
      CONFIRMED: {
        color: '#3b82f6',
        bgColor: '#dbeafe',
        icon: '✅',
        title: 'Order Confirmed'
      },
      PROCESSING: {
        color: '#8b5cf6',
        bgColor: '#ede9fe',
        icon: '📦',
        title: 'Order Processing'
      },
      SHIPPED: {
        color: '#f59e0b',
        bgColor: '#fef3c7',
        icon: '🚚',
        title: 'Order Shipped'
      },
      DELIVERED: {
        color: '#10b981',
        bgColor: '#d1fae5',
        icon: '🎉',
        title: 'Order Delivered'
      },
      CANCELLED: {
        color: '#ef4444',
        bgColor: '#fee2e2',
        icon: '❌',
        title: 'Order Cancelled'
      }
    };

    const config = statusConfig[data.status as keyof typeof statusConfig] || statusConfig.CONFIRMED;

    return {
      subject: `${config.title} - ${data.orderNumber} | RIVV`,
      html: `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Order Status Update</title>
            <style>
              body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
              .container { max-width: 600px; margin: 0 auto; background-color: white; }
              .header { background: linear-gradient(135deg, #1f2937 0%, #374151 100%); color: white; padding: 30px 20px; text-align: center; }
              .brand { font-size: 28px; font-weight: bold; margin-bottom: 8px; letter-spacing: 2px; }
              .tagline { font-size: 14px; opacity: 0.9; font-style: italic; }
              .content { padding: 30px 20px; }
              .status-badge { display: inline-block; background-color: ${config.bgColor}; color: ${config.color}; padding: 12px 20px; border-radius: 25px; font-size: 14px; font-weight: bold; margin-bottom: 25px; border: 2px solid ${config.color}; }
              .status-icon { font-size: 24px; margin-right: 10px; }
              .order-summary { background-color: #f9fafb; padding: 25px; border-radius: 12px; margin: 25px 0; border-left: 4px solid ${config.color}; }
              .tracking-info { background-color: ${config.bgColor}; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid ${config.color}; }
              .item { border-bottom: 1px solid #e5e7eb; padding: 12px 0; display: flex; justify-content: space-between; align-items: center; }
              .item:last-child { border-bottom: none; }
              .item-info { flex: 1; }
              .item-name { font-weight: 600; color: #1f2937; margin-bottom: 4px; }
              .item-details { font-size: 14px; color: #6b7280; }
              .item-price { font-weight: bold; color: #1f2937; }
              .button { display: inline-block; background: linear-gradient(135deg, #1f2937 0%, #374151 100%); color: white; padding: 14px 28px; text-decoration: none; border-radius: 8px; margin: 25px 0; font-weight: 600; text-align: center; }
              .contact-info { background-color: #f0fdf4; padding: 20px; border-radius: 8px; border-left: 4px solid #10b981; margin: 25px 0; }
              .footer { background-color: #f9fafb; padding: 25px 20px; text-align: center; color: #6b7280; font-size: 14px; border-top: 1px solid #e5e7eb; }
              .unsubscribe { font-size: 12px; color: #9ca3af; margin-top: 15px; }
              .unsubscribe a { color: #9ca3af; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <div class="brand">RIVV</div>
                <div class="tagline">Purposefully Curated. Unapologetically Premium.</div>
              </div>

              <div class="content">
                <div class="status-badge">
                  <span class="status-icon">${config.icon}</span>
                  ${config.title}
                </div>

                <h2>Hi ${data.customerName}!</h2>
                <p>${data.statusMessage}</p>

                <div class="order-summary">
                  <h3 style="margin-top: 0; color: #1f2937;">Order #${data.orderNumber}</h3>

                  <h4 style="margin-bottom: 15px; color: #1f2937;">Items:</h4>
                  ${data.orderItems.map(item => `
                    <div class="item">
                      <div class="item-info">
                        <div class="item-name">${item.name}</div>
                        <div class="item-details">
                          Quantity: ${item.quantity}${item.size ? ` • Size: ${item.size}` : ''}
                        </div>
                      </div>
                      <div class="item-price">${item.price}</div>
                    </div>
                  `).join('')}

                  <div style="text-align: right; margin-top: 15px; padding-top: 15px; border-top: 2px solid ${config.color};">
                    <strong style="font-size: 18px; color: #1f2937;">Total: ${data.orderTotal}</strong>
                  </div>
                </div>

                ${data.trackingInfo ? `
                  <div class="tracking-info">
                    <h4 style="margin-top: 0; color: ${config.color};">Tracking Information</h4>
                    <p style="margin: 0; font-family: monospace; font-weight: bold;">${data.trackingInfo}</p>
                    ${data.estimatedDelivery ? `<p style="margin: 10px 0 0 0;"><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>` : ''}
                  </div>
                ` : ''}

                ${data.cancellationReason ? `
                  <div class="tracking-info">
                    <h4 style="margin-top: 0; color: ${config.color};">Cancellation Reason</h4>
                    <p style="margin: 0;">${data.cancellationReason}</p>
                  </div>
                ` : ''}

                <div style="text-align: center;">
                  <a href="${data.orderUrl}" class="button">View Order Details</a>
                </div>

                <div class="contact-info">
                  <h4 style="margin-top: 0; color: #059669;">Need Help?</h4>
                  <p style="margin: 0;">For any questions or concerns, contact our support team:</p>
                  <p style="margin: 5px 0 0 0;"><strong>WhatsApp:</strong> +266 62844473</p>
                  <p style="margin: 5px 0 0 0;"><strong>Email:</strong> <EMAIL></p>
                </div>
              </div>

              <div class="footer">
                <p><strong>RIVV Premium Sneakers</strong><br>
                Purposefully Curated. Unapologetically Premium.</p>
                <p>© 2025 RIVV. All rights reserved.</p>
                <div class="unsubscribe">
                  <p>You received this email because you have an order with us.<br>
                  <a href="#">Unsubscribe from order updates</a></p>
                </div>
              </div>
            </div>
          </body>
        </html>
      `,
      text: `
        RIVV - ${config.title}
        Purposefully Curated. Unapologetically Premium.

        ${config.icon} ${config.title.toUpperCase()}

        Hi ${data.customerName}!

        ${data.statusMessage}

        Order #${data.orderNumber}

        Items:
        ${data.orderItems.map(item => `${item.name} - Qty: ${item.quantity}${item.size ? ` (Size: ${item.size})` : ''} - ${item.price}`).join('\n')}

        Total: ${data.orderTotal}

        ${data.trackingInfo ? `Tracking Information: ${data.trackingInfo}` : ''}
        ${data.estimatedDelivery ? `Estimated Delivery: ${data.estimatedDelivery}` : ''}
        ${data.cancellationReason ? `Cancellation Reason: ${data.cancellationReason}` : ''}

        View order details: ${data.orderUrl}

        Need Help?
        WhatsApp: +266 62844473
        Email: <EMAIL>

        RIVV Premium Sneakers
        © 2025 RIVV. All rights reserved.
      `,
    };
  },

  // Lay-Buy reminder emails
  layBuyWeeklyReminder: (data: {
    customerName: string;
    orderNumber: string;
    totalAmount: string;
    amountPaid: string;
    remainingAmount: string;
    dueDate: string;
    daysRemaining: number;
    weekNumber: number;
    paymentInstructions: string;
    orderUrl: string;
  }) => ({
    subject: `💳 Lay-Buy Payment Reminder - Week ${data.weekNumber} | Order ${data.orderNumber}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Lay-Buy Payment Reminder</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #3b82f6; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
            .payment-summary { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3b82f6; }
            .progress-bar { background: #e5e7eb; height: 8px; border-radius: 4px; margin: 10px 0; }
            .progress-fill { background: #10b981; height: 100%; border-radius: 4px; }
            .cta-button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>💳 Lay-Buy Payment Reminder</h1>
              <p>Week ${data.weekNumber} - ${data.daysRemaining} days remaining</p>
            </div>

            <div class="content">
              <h2>Hi ${data.customerName},</h2>

              <p>This is your weekly reminder about your Lay-Buy order <strong>${data.orderNumber}</strong>.</p>

              <div class="payment-summary">
                <h3>Payment Summary</h3>
                <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                  <span>Total Order Value:</span>
                  <strong>${data.totalAmount}</strong>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                  <span>Amount Paid:</span>
                  <strong style="color: #10b981;">${data.amountPaid}</strong>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                  <span>Remaining Balance:</span>
                  <strong style="color: #f59e0b;">${data.remainingAmount}</strong>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                  <span>Payment Due Date:</span>
                  <strong>${data.dueDate}</strong>
                </div>
              </div>

              <p><strong>⏰ You have ${data.daysRemaining} days remaining</strong> to complete your payment.</p>

              <h3>Payment Instructions</h3>
              <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #e5e7eb;">
                ${data.paymentInstructions}
              </div>

              <div style="text-align: center;">
                <a href="${data.orderUrl}" class="cta-button">View Order Details</a>
              </div>

              <p>If you have any questions or need assistance, please contact our support team.</p>

              <p>Thank you for choosing RIVV!</p>
            </div>

            <div class="footer">
              <p>RIVV - Your Fashion Destination</p>
              <p>This is an automated reminder. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      Lay-Buy Payment Reminder - Week ${data.weekNumber}

      Hi ${data.customerName},

      This is your weekly reminder about your Lay-Buy order ${data.orderNumber}.

      Payment Summary:
      - Total Order Value: ${data.totalAmount}
      - Amount Paid: ${data.amountPaid}
      - Remaining Balance: ${data.remainingAmount}
      - Payment Due Date: ${data.dueDate}

      You have ${data.daysRemaining} days remaining to complete your payment.

      Payment Instructions:
      ${data.paymentInstructions}

      View your order details: ${data.orderUrl}

      If you have any questions, please contact our support team.

      Thank you for choosing RIVV!
    `
  }),

  layBuyUrgentReminder: (data: {
    customerName: string;
    orderNumber: string;
    totalAmount: string;
    amountPaid: string;
    remainingAmount: string;
    dueDate: string;
    daysRemaining: number;
    paymentInstructions: string;
    orderUrl: string;
  }) => ({
    subject: `🚨 URGENT: Lay-Buy Payment Due Soon - ${data.daysRemaining} Days Left | Order ${data.orderNumber}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Urgent Lay-Buy Payment Reminder</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #fef2f2; padding: 30px; border-radius: 0 0 8px 8px; border: 2px solid #dc2626; }
            .urgent-notice { background: #dc2626; color: white; padding: 15px; border-radius: 6px; text-align: center; margin: 20px 0; }
            .payment-summary { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626; }
            .cta-button { display: inline-block; background: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: bold; }
            .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🚨 URGENT PAYMENT REMINDER</h1>
              <p>Your Lay-Buy payment is due in ${data.daysRemaining} days!</p>
            </div>

            <div class="content">
              <div class="urgent-notice">
                <h2>⚠️ IMMEDIATE ACTION REQUIRED</h2>
                <p>Your Lay-Buy order ${data.orderNumber} payment is due very soon!</p>
              </div>

              <h2>Hi ${data.customerName},</h2>

              <p><strong>This is an urgent reminder</strong> that your Lay-Buy payment is due in just <strong>${data.daysRemaining} days</strong>.</p>

              <div class="payment-summary">
                <h3>Outstanding Payment</h3>
                <div style="display: flex; justify-content: space-between; margin: 15px 0; font-size: 18px;">
                  <span>Amount Due:</span>
                  <strong style="color: #dc2626; font-size: 24px;">${data.remainingAmount}</strong>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                  <span>Due Date:</span>
                  <strong>${data.dueDate}</strong>
                </div>
              </div>

              <p><strong>⚠️ Important:</strong> If payment is not received by the due date, you will enter a 1-week grace period. After that, your order may be forfeited.</p>

              <h3>Payment Instructions</h3>
              <div style="background: white; padding: 15px; border-radius: 6px; border: 2px solid #dc2626;">
                ${data.paymentInstructions}
              </div>

              <div style="text-align: center;">
                <a href="${data.orderUrl}" class="cta-button">PAY NOW - VIEW ORDER</a>
              </div>

              <p>Please complete your payment as soon as possible to secure your order.</p>

              <p>If you need assistance or have any questions, please contact us immediately.</p>
            </div>

            <div class="footer">
              <p>RIVV - Your Fashion Destination</p>
              <p>This is an automated urgent reminder.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      🚨 URGENT: Lay-Buy Payment Due Soon - ${data.daysRemaining} Days Left

      Hi ${data.customerName},

      This is an urgent reminder that your Lay-Buy payment is due in just ${data.daysRemaining} days.

      Order: ${data.orderNumber}
      Amount Due: ${data.remainingAmount}
      Due Date: ${data.dueDate}

      IMPORTANT: If payment is not received by the due date, you will enter a 1-week grace period. After that, your order may be forfeited.

      Payment Instructions:
      ${data.paymentInstructions}

      View your order and pay now: ${data.orderUrl}

      Please complete your payment as soon as possible to secure your order.

      Contact us immediately if you need assistance.
    `
  }),

  layBuyGracePeriodReminder: (data: {
    customerName: string;
    orderNumber: string;
    totalAmount: string;
    amountPaid: string;
    remainingAmount: string;
    gracePeriodEnd: string;
    daysRemaining: number;
    paymentInstructions: string;
    orderUrl: string;
  }) => ({
    subject: `⚠️ FINAL NOTICE: Grace Period - ${data.daysRemaining} Days to Save Your Order | ${data.orderNumber}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Final Notice - Grace Period</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #b91c1c; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #fef1f1; padding: 30px; border-radius: 0 0 8px 8px; border: 3px solid #b91c1c; }
            .final-notice { background: #b91c1c; color: white; padding: 20px; border-radius: 6px; text-align: center; margin: 20px 0; }
            .payment-summary { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #b91c1c; }
            .cta-button { display: inline-block; background: #b91c1c; color: white; padding: 18px 36px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: bold; font-size: 18px; }
            .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>⚠️ FINAL NOTICE - GRACE PERIOD</h1>
              <p>Only ${data.daysRemaining} days left to save your order!</p>
            </div>

            <div class="content">
              <div class="final-notice">
                <h2>🚨 LAST CHANCE TO COMPLETE PAYMENT</h2>
                <p>Order ${data.orderNumber} will be forfeited in ${data.daysRemaining} days</p>
              </div>

              <h2>Hi ${data.customerName},</h2>

              <p><strong>This is your final notice.</strong> Your Lay-Buy order has entered the grace period and will be <strong>permanently forfeited</strong> if payment is not received within <strong>${data.daysRemaining} days</strong>.</p>

              <div class="payment-summary">
                <h3>⚠️ FINAL PAYMENT REQUIRED</h3>
                <div style="display: flex; justify-content: space-between; margin: 15px 0; font-size: 20px;">
                  <span>Amount Due:</span>
                  <strong style="color: #b91c1c; font-size: 28px;">${data.remainingAmount}</strong>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                  <span>Grace Period Ends:</span>
                  <strong style="color: #b91c1c;">${data.gracePeriodEnd}</strong>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                  <span>Days Remaining:</span>
                  <strong style="color: #b91c1c; font-size: 18px;">${data.daysRemaining} DAYS</strong>
                </div>
              </div>

              <p><strong>⚠️ IMPORTANT:</strong> After the grace period ends:</p>
              <ul style="color: #b91c1c; font-weight: bold;">
                <li>Your order will be permanently forfeited</li>
                <li>No refund will be provided</li>
                <li>Products will be returned to inventory</li>
              </ul>

              <h3>Payment Instructions - ACT NOW</h3>
              <div style="background: white; padding: 20px; border-radius: 6px; border: 3px solid #b91c1c;">
                ${data.paymentInstructions}
              </div>

              <div style="text-align: center;">
                <a href="${data.orderUrl}" class="cta-button">SAVE MY ORDER - PAY NOW</a>
              </div>

              <p style="color: #b91c1c; font-weight: bold; text-align: center;">Don't lose your order! Complete payment today.</p>
            </div>

            <div class="footer">
              <p>RIVV - Your Fashion Destination</p>
              <p>This is your final automated notice.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
      ⚠️ FINAL NOTICE - GRACE PERIOD - ${data.daysRemaining} Days Left

      Hi ${data.customerName},

      This is your final notice. Your Lay-Buy order ${data.orderNumber} has entered the grace period and will be permanently forfeited if payment is not received within ${data.daysRemaining} days.

      FINAL PAYMENT REQUIRED: ${data.remainingAmount}
      Grace Period Ends: ${data.gracePeriodEnd}
      Days Remaining: ${data.daysRemaining} DAYS

      IMPORTANT: After the grace period ends:
      - Your order will be permanently forfeited
      - No refund will be provided
      - Products will be returned to inventory

      Payment Instructions - ACT NOW:
      ${data.paymentInstructions}

      Save your order - Pay now: ${data.orderUrl}

      Don't lose your order! Complete payment today.
    `
  }),
};
